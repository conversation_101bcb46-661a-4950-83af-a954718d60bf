# ButtonGroup 组件

一个支持按钮数量限制和"更多"按钮的按钮组组件。当按钮数量超过指定的最大值时，会自动显示一个"更多"按钮，将多余的按钮收纳到下拉菜单中。

## 功能特性

- ✅ 支持按钮数量限制（默认最多显示3个）
- ✅ 超出数量时自动显示"更多"按钮
- ✅ 支持嵌套子菜单（二级菜单）
- ✅ 支持自定义样式类名
- ✅ 支持自定义"更多"按钮图标
- ✅ 支持按钮隐藏和禁用状态

## Props

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `list` | `Item[]` | - | 按钮列表数据 |
| `className` | `{ outer?: string; item?: string }` | - | 自定义样式类名 |
| `max` | `number` | `3` | 最大显示按钮数量 |
| `moreIconName` | `string` | `"more"` | "更多"按钮的图标名称 |

## Item 接口

```typescript
interface Item {
  name: string;           // 按钮显示名称
  iconName: string;       // 按钮图标名称
  hidden?: boolean;       // 是否隐藏（隐藏的按钮不会被计算在内）
  disabled?: boolean;     // 是否禁用
  children?: Item[];      // 子菜单项
  onClick?: () => void;   // 点击事件处理函数
}
```

## 使用示例

### 基础用法

```vue
<template>
  <ButtonGroup :list="buttonList" />
</template>

<script setup>
const buttonList = [
  {
    name: '上传',
    iconName: 'upload',
    onClick: () => console.log('上传')
  },
  {
    name: '下载',
    iconName: 'download',
    onClick: () => console.log('下载')
  }
];
</script>
```

### 超过最大数量的按钮

```vue
<template>
  <ButtonGroup :list="longButtonList" />
</template>

<script setup>
const longButtonList = [
  { name: '按钮1', iconName: 'icon1', onClick: () => {} },
  { name: '按钮2', iconName: 'icon2', onClick: () => {} },
  { name: '按钮3', iconName: 'icon3', onClick: () => {} },
  { name: '按钮4', iconName: 'icon4', onClick: () => {} }, // 这个会被收纳到"更多"中
  { name: '按钮5', iconName: 'icon5', onClick: () => {} }  // 这个也会被收纳到"更多"中
];
</script>
```

### 带有子菜单的按钮

```vue
<template>
  <ButtonGroup :list="menuList" />
</template>

<script setup>
const menuList = [
  {
    name: '文件',
    iconName: 'file',
    children: [
      {
        name: '新建',
        iconName: 'file-add',
        onClick: () => console.log('新建文件')
      },
      {
        name: '打开',
        iconName: 'file-open',
        onClick: () => console.log('打开文件')
      }
    ]
  }
];
</script>
```

### 自定义配置

```vue
<template>
  <ButtonGroup 
    :list="buttonList" 
    :max="2" 
    more-icon-name="menu"
    :className="{ 
      outer: 'custom-outer-class', 
      item: 'custom-item-class' 
    }"
  />
</template>
```

## 行为说明

1. **按钮数量控制**: 当可见按钮数量 ≤ `max` 时，所有按钮正常显示
2. **更多按钮显示**: 当可见按钮数量 > `max` 时，显示前 `max-1` 个按钮，剩余按钮收纳到"更多"按钮中
3. **隐藏按钮处理**: 设置了 `hidden: true` 的按钮不会被显示，也不会被计算在数量限制内
4. **嵌套菜单**: 在"更多"按钮的下拉菜单中，有子菜单的项目会显示为二级下拉菜单
5. **事件处理**: 每个按钮的点击事件通过 `onClick` 回调函数处理

## 样式定制

组件支持通过 `className` prop 传入自定义样式类名：

- `className.outer`: 应用到最外层 ElSpace 组件
- `className.item`: 应用到每个按钮项

## 注意事项

- 确保传入的图标名称在项目的图标库中存在
- 子菜单项也支持再次嵌套，但建议不要超过两级以保持良好的用户体验
- 在"更多"按钮的下拉菜单中，有子菜单的项目使用 hover 触发，无子菜单的项目使用 click 触发
