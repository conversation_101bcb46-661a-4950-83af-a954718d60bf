<script lang="ts" setup>
import { defineProps, computed, h } from "vue";
import { ElS<PERSON>, ElD<PERSON>ider, ElDropdown, ElDropdownMenu, ElDropdownItem } from "element-plus";
import { SvgIcon } from "@tg-fe/ui";

interface Item {
  name: string;
  iconName: string;
  hidden?: boolean;
  disabled?: boolean;
  children?: Item[];
  onClick?: () => void;
}
interface Props {
  list: Item[];
  className?: {
    outer: string;
    item: string;
  };
  max?: number;
  moreIconName?: string; // 更多按钮的图标名称
}
const { list, className, max = 3, moreIconName = "more" } = defineProps<Props>();

// 计算显示的项目和更多项目
const displayItems = computed(() => {
  const visibleList = list.filter((item) => !item.hidden);
  if (visibleList.length <= max) {
    return {
      visible: visibleList,
      more: [],
    };
  }
  return {
    visible: visibleList.slice(0, max - 1),
    more: visibleList.slice(max - 1),
  };
});

const spacer = h(<PERSON><PERSON><PERSON><PERSON>, { direction: "vertical", class: "h-3 mx-0 !border-[var(--line-line-2)] !leading-0" });
</script>

<template>
  <ElSpace :class="`rounded-[12px] ${className?.outer}`" :size="0" :spacer="spacer">
    <!-- 显示的常规按钮 -->
    <div v-for="item in displayItems.visible" :key="item.name" @click="item.onClick">
      <ElDropdown popper-class="border-none rounded-[12px] !shadow-[0_3px_12px_0_rgba(0,8,24,0.12)] mt-[-8px]">
        <div
          :class="`flex h-9 shrink-0 cursor-pointer items-center gap-[6px] px-4 text-[14px]/[1.5] outline-none ${className?.item}`"
        >
          <SvgIcon class="h-[18px] w-[18px]" :name="item.iconName"></SvgIcon>
          <div>{{ item.name }}</div>
        </div>
        <template #dropdown>
          <ElDropdownMenu v-if="item.children">
            <ElDropdownItem
              v-for="child in item.children"
              :class="`flex items-center ${className?.item}`"
              :key="child.name"
              @click="child.onClick"
            >
              <SvgIcon class="mr-2 h-[16px] w-[16px]" :name="child.iconName"></SvgIcon>
              <div>{{ child.name }}</div>
            </ElDropdownItem>
          </ElDropdownMenu>
        </template>
      </ElDropdown>
    </div>

    <!-- 更多按钮 -->
    <div v-if="displayItems.more.length > 0">
      <ElDropdown popper-class="border-none rounded-[12px] !shadow-[0_3px_12px_0_rgba(0,8,24,0.12)] mt-[-8px]">
        <div
          :class="`flex h-9 shrink-0 cursor-pointer items-center gap-[6px] px-4 text-[14px]/[1.5] outline-none ${className?.item}`"
        >
          <SvgIcon class="h-[18px] w-[18px]" :name="moreIconName"></SvgIcon>
        </div>
        <template #dropdown>
          <ElDropdownMenu>
            <template v-for="item in displayItems.more" :key="item.name">
              <!-- 如果有子项，显示为嵌套下拉 -->
              <ElDropdownItem class="!p-0" v-if="item.children && item.children.length > 0">
                <ElDropdown
                  placement="right-start"
                  popper-class="border-none rounded-[12px] !shadow-[0_3px_12px_0_rgba(0,8,24,0.12)]"
                  trigger="hover"
                >
                  <div
                    :class="`flex w-full items-center px-3 py-2 hover:bg-gray-50 ${className?.item}`"
                    @click="item.onClick"
                  >
                    <SvgIcon class="mr-2 h-[16px] w-[16px]" :name="item.iconName"></SvgIcon>
                    <div class="flex-1">{{ item.name }}</div>
                    <SvgIcon class="h-[12px] w-[12px] text-gray-400" name="arrow-right"></SvgIcon>
                  </div>
                  <template #dropdown>
                    <ElDropdownMenu>
                      <ElDropdownItem
                        v-for="child in item.children"
                        :class="`flex items-center ${className?.item}`"
                        :key="child.name"
                        @click="child.onClick"
                      >
                        <SvgIcon class="mr-2 h-[16px] w-[16px]" :name="child.iconName"></SvgIcon>
                        <div>{{ child.name }}</div>
                      </ElDropdownItem>
                    </ElDropdownMenu>
                  </template>
                </ElDropdown>
              </ElDropdownItem>
              <!-- 如果没有子项，显示为普通项 -->
              <ElDropdownItem v-else :class="`flex items-center ${className?.item}`" @click="item.onClick">
                <SvgIcon class="mr-2 h-[16px] w-[16px]" :name="item.iconName"></SvgIcon>
                <div>{{ item.name }}</div>
              </ElDropdownItem>
            </template>
          </ElDropdownMenu>
        </template>
      </ElDropdown>
    </div>
  </ElSpace>
</template>

<style lang="scss" scoped>
.el-space--horizontal {
  :deep(span) {
    line-height: 0 !important;
  }
}
</style>
