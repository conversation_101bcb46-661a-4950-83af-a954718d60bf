<script lang="ts" setup>
import { defineProps, computed } from "vue";
import { ElSpace, ElDivider } from "element-plus";
import { SvgIcon } from "@tg-fe/ui";

interface Item {
  name: string;
  iconName: string;
  hidden?: boolean;
  disabled?: boolean;
  children?: Item[];
  onClick?: () => void;
}
interface Props {
  list: Item[];
  className?: {
    outer: string;
    item: string;
  };
  max?: number;
}
const { list, className, max = 3 } = defineProps<Props>();

const spacer = h(ElDivider, { direction: "vertical", class: "h-3 mx-0 !border-[var(--line-line-2)] !leading-0" });
</script>

<template>
  <ElSpace :class="`rounded-[12px] ${className?.outer}`" :size="0" :spacer="spacer">
    <div v-for="item in list" :key="item.name" @click="item.onClick">
      <ElDropdown popper-class="border-none rounded-[12px] !shadow-[0_3px_12px_0_rgba(0,8,24,0.12)] mt-[-8px]">
        <div
          :class="`flex h-9 shrink-0 cursor-pointer items-center gap-[6px] px-4 text-[14px]/[1.5] outline-none ${className?.item}`"
        >
          <SvgIcon class="h-[18px] w-[18px]" :name="item.iconName"></SvgIcon>
          <div>{{ item.name }}</div>
        </div>
        <template #dropdown>
          <ElDropdownMenu v-if="item.children">
            <ElDropdownItem
              v-for="child in item.children"
              :class="`flex items-center ${className?.item}`"
              :key="child.name"
              @click="child.onClick"
            >
              <SvgIcon class="mr-2 h-[16px] w-[16px]" :name="child.iconName"></SvgIcon>
              <div>{{ child.name }}</div>
            </ElDropdownItem>
          </ElDropdownMenu>
        </template>
      </ElDropdown>
    </div>
  </ElSpace>
</template>

<style lang="scss" scoped>
.el-space--horizontal {
  :deep(span) {
    line-height: 0 !important;
  }
}
</style>
