<template>
  <div class="p-4 space-y-4">
    <h2 class="text-lg font-bold">ButtonGroup 组件示例</h2>
    
    <!-- 示例1: 少于3个按钮 -->
    <div>
      <h3 class="text-md font-semibold mb-2">示例1: 少于3个按钮（正常显示）</h3>
      <ButtonGroup :list="shortList" />
    </div>
    
    <!-- 示例2: 超过3个按钮 -->
    <div>
      <h3 class="text-md font-semibold mb-2">示例2: 超过3个按钮（显示更多按钮）</h3>
      <ButtonGroup :list="longList" />
    </div>
    
    <!-- 示例3: 带有子项的按钮 -->
    <div>
      <h3 class="text-md font-semibold mb-2">示例3: 带有子项的按钮</h3>
      <ButtonGroup :list="listWithChildren" />
    </div>
    
    <!-- 示例4: 自定义最大显示数量 -->
    <div>
      <h3 class="text-md font-semibold mb-2">示例4: 自定义最大显示数量（max=2）</h3>
      <ButtonGroup :list="longList" :max="2" />
    </div>
    
    <!-- 示例5: 自定义更多按钮图标 -->
    <div>
      <h3 class="text-md font-semibold mb-2">示例5: 自定义更多按钮图标</h3>
      <ButtonGroup :list="longList" more-icon-name="menu" />
    </div>
  </div>
</template>

<script lang="ts" setup>
import ButtonGroup from './ButtonGroup.vue';

// 少于3个按钮的列表
const shortList = [
  {
    name: '上传',
    iconName: 'upload',
    onClick: () => console.log('上传点击')
  },
  {
    name: '下载',
    iconName: 'download',
    onClick: () => console.log('下载点击')
  }
];

// 超过3个按钮的列表
const longList = [
  {
    name: '上传',
    iconName: 'upload',
    onClick: () => console.log('上传点击')
  },
  {
    name: '下载',
    iconName: 'download',
    onClick: () => console.log('下载点击')
  },
  {
    name: '分享',
    iconName: 'share',
    onClick: () => console.log('分享点击')
  },
  {
    name: '删除',
    iconName: 'delete',
    onClick: () => console.log('删除点击')
  },
  {
    name: '编辑',
    iconName: 'edit',
    onClick: () => console.log('编辑点击')
  }
];

// 带有子项的按钮列表
const listWithChildren = [
  {
    name: '文件操作',
    iconName: 'file',
    children: [
      {
        name: '新建文件',
        iconName: 'file-add',
        onClick: () => console.log('新建文件点击')
      },
      {
        name: '打开文件',
        iconName: 'file-open',
        onClick: () => console.log('打开文件点击')
      }
    ]
  },
  {
    name: '编辑',
    iconName: 'edit',
    onClick: () => console.log('编辑点击')
  },
  {
    name: '视图',
    iconName: 'view',
    children: [
      {
        name: '列表视图',
        iconName: 'list',
        onClick: () => console.log('列表视图点击')
      },
      {
        name: '网格视图',
        iconName: 'grid',
        onClick: () => console.log('网格视图点击')
      },
      {
        name: '详细视图',
        iconName: 'detail',
        onClick: () => console.log('详细视图点击')
      }
    ]
  },
  {
    name: '工具',
    iconName: 'tool',
    children: [
      {
        name: '设置',
        iconName: 'settings',
        onClick: () => console.log('设置点击')
      },
      {
        name: '帮助',
        iconName: 'help',
        onClick: () => console.log('帮助点击')
      }
    ]
  },
  {
    name: '导出',
    iconName: 'export',
    onClick: () => console.log('导出点击')
  },
  {
    name: '导入',
    iconName: 'import',
    onClick: () => console.log('导入点击')
  }
];
</script>

<style scoped>
.space-y-4 > * + * {
  margin-top: 1rem;
}
</style>
