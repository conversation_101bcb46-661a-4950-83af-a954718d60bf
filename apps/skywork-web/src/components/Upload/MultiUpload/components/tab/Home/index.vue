<script lang="ts" setup>
import { isDefaultTab } from "@/components/Upload/MultiUpload/utils/isDefaultTab";
import UploadButtonGroup from "@/components/Upload/MultiUpload/components/tab/Home/components/UploadButtonGroup.vue";
import FileList, { MultiUploadHomeItem } from "./components/FileList.vue";
import { ElButton } from "element-plus";

interface Props {
  currentComponent: string;
  setCurrentComponent: (component: string) => void;
}

const { currentComponent, setCurrentComponent } = defineProps<Props>();

const list: MultiUploadHomeItem[] = [
  { name: "xxx.pptx", isTemplate: true, icon: "ic_files_ppt" },
  { name: "xxx2.ppt", icon: "ic_files_ppt" },
  { name: "xxx.pptx", isTemplate: true, icon: "ic_files_ppt" },
  { name: "xxx2.ppt", icon: "ic_files_ppt" },
  { name: "xxx.pptx", isTemplate: true, icon: "ic_files_ppt" },
  { name: "xxx2.ppt", icon: "ic_files_ppt" },
  { name: "xxx.pptx", isTemplate: true, icon: "ic_files_ppt" },
  { name: "xxx2.ppt", icon: "ic_files_ppt" },
];
</script>

<template>
  <div>
    <UploadButtonGroup
      v-if="isDefaultTab(currentComponent)"
      :setCurrentComponent="setCurrentComponent"
    ></UploadButtonGroup>
    <div class="mb-[16px] h-[356px] overflow-y-auto">
      <FileList :list="list" />
    </div>

    <div class="flex justify-end pt-5">
      <ElButton color="black">确定</ElButton>
    </div>
  </div>
</template>
