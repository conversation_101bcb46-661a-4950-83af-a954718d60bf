<script setup lang="ts">
export interface MultiUploadHomeItem {
  name: string;
  isTemplate?: boolean;
  icon: string;
}
interface Props {
  list: MultiUploadHomeItem[];
}

const { list } = defineProps<Props>();
</script>
<template>
  <div class="space-y-[8px]">
    <div
      class="hover:bg-fill-fill-4 flex h-[44px] items-center justify-between rounded-[12px] px-[12px]"
      v-for="{ name, isTemplate, icon } in list"
      :key="name"
    >
      <div class="flex items-center gap-[6px]">
        <SvgIcon class="h-[28px] w-[28px]" :name="icon" />
        <div class="text-text-icon-text-3 text-[15px]/[1.5]">{{ name }}</div>
        <div
          class="bg-ppt-magenta-magenta50 text-ppt-magenta flex h-[25px] items-center rounded-[8px] px-[12px] text-[14px]/[1.5]"
          v-if="isTemplate"
        >
          PPT 模板
        </div>
      </div>

      <div class="text-text-icon-text-3 flex items-center gap-[16px] text-[14px]/[1.5]">
        <div class="flex cursor-pointer items-center" v-if="isTemplate">
          <SvgIcon class="mr-[4px] h-[18px] w-[18px]" name="ic_edit" />
          <div>取消模版</div>
        </div>
        <div class="flex cursor-pointer items-center" v-else>
          <SvgIcon class="mr-[4px] h-[18px] w-[18px]" name="ic_edit" />
          <div>设为模版</div>
        </div>
        <div class="flex cursor-pointer items-center">
          <SvgIcon class="mr-[4px] h-[18px] w-[18px]" name="ic_edit" />
          <div>删除</div>
        </div>
      </div>
    </div>
  </div>
</template>
