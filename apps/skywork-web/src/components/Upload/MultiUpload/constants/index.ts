import CloudImport from "@/components/Upload/MultiUpload/components/tab/CloudImport.vue";
import Home from "@/components/Upload/MultiUpload/components/tab/Home/index.vue";
import Knowledge from "@/components/Upload/MultiUpload/components/tab/Knowledge.vue";
import LinkPaste from "@/components/Upload/MultiUpload/components/tab/LinkPaste.vue";
import Local from "@/components/Upload/MultiUpload/components/tab/Local.vue";
import PPTSelect from "@/components/Upload/MultiUpload/components/tab/PPTSelect.vue";
import PPTUpload from "@/components/Upload/MultiUpload/components/tab/PPTUpload.vue";
import TextPaste from "@/components/Upload/MultiUpload/components/tab/TextPaste/index.vue";
import { Component } from "vue";

interface Detail {
  title: string;
  components: Component;
  tips?: string;
}
export const COMPONENT_MAP: Record<string, Detail> = {
  home: { title: "Upload", components: Home, tips: "这是 tips" },
  pptSelect: { title: "Select PPT Template", components: PPTSelect },
  pptUpload: { title: "Upload PPT Template", components: PPTUpload },
  local: { title: "Local Upload", components: Local },
  knowledge: { title: "Knowledge Base", components: Knowledge },
  linkPaste: { title: "Paste Link", components: LinkPaste },
  textPaste: { title: "Paste Text", components: TextPaste },
  cloud: { title: "Cloud Import", components: CloudImport },
};
